/* 滚动性能优化 - 解决滚动卡顿和帧率问题 */

/* 全局滚动优化 - 性能优先版本 */
html {
  /* 移除硬件加速，避免GPU过载 */
  -webkit-overflow-scrolling: auto;
  /* 允许正常的过度滚动行为 */
  overscroll-behavior: auto;
  -webkit-overscroll-behavior: auto;
  /* 移除平滑滚动，减少动画开销 */
  scroll-behavior: auto;
  overflow-x: hidden;
}

body {
  /* 移除硬件加速，避免GPU过载 */
  -webkit-overflow-scrolling: auto;
  /* 允许正常的过度滚动行为 */
  overscroll-behavior: auto;
  -webkit-overscroll-behavior: auto;
  /* 移除平滑滚动，减少动画开销 */
  scroll-behavior: auto;
  overflow-x: hidden;
}

/* 主容器滚动优化 - 性能优先版本 */
.welcome-page {
  /* 移除硬件加速，避免GPU过载 */
  -webkit-overflow-scrolling: auto;
  /* 允许正常的过度滚动行为 */
  overscroll-behavior: auto;
  -webkit-overscroll-behavior: auto;
  /* 移除平滑滚动，减少动画开销 */
  scroll-behavior: auto;
  /* 移除transform，减少GPU负载 */
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 减少动画对滚动的影响 - 移除过度优化 */
.feature-particle,
.particle,
.floating-digit,
.binary-digit {
  /* 移除will-change，减少GPU负载 */
  /* 移除transform，减少硬件加速 */
  backface-visibility: hidden;
}

/* 优化下拉菜单的滚动处理 */
.dropdown-menu {
  /* 允许独立滚动 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* 优化地图控件的滚动处理 */
.migration-controls {
  /* 允许独立滚动 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
  html, body {
    /* 移动端滚动优化 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: auto;
    overscroll-behavior-x: hidden;
  }
  
  .welcome-page {
    /* 移动端滚动优化 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: auto;
    overscroll-behavior-x: hidden;
  }
}

/* 高DPI屏幕滚动优化 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  html, body, .welcome-page {
    /* 高DPI屏幕滚动优化 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: auto;
  }
}

/* 减少动画在滚动时的性能影响 */
@media (prefers-reduced-motion: no-preference) {
  /* 只在用户允许动画时启用复杂动画 */
  .feature-particle {
    animation: particleFloat linear infinite;
  }
  
  .floating-digit {
    animation: floatingDigitAnimation 8s ease-in-out infinite;
  }
}

@media (prefers-reduced-motion: reduce) {
  /* 用户偏好减少动画时，禁用所有动画 */
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

/* Firefox滚动条隐藏 */
html {
  scrollbar-width: none;
}

/* 确保所有容器都隐藏滚动条但保持滚动功能 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}
